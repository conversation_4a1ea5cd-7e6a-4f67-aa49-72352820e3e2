{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Calculate estimated reading time for a text\n * @param text - The text content to analyze\n * @param wordsPerMinute - Average reading speed (default: 200 words per minute)\n * @returns Estimated reading time in minutes\n */\nexport function calculateReadingTime(text: string, wordsPerMinute: number = 200): number {\n  // Remove markdown syntax and HTML tags for more accurate word count\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  const wordCount = cleanText.split(' ').filter(word => word.length > 0).length\n  const readingTime = Math.ceil(wordCount / wordsPerMinute)\n  \n  return Math.max(1, readingTime) // Minimum 1 minute\n}\n\n/**\n * Create an excerpt from text content\n * @param text - The full text content\n * @param maxLength - Maximum length of the excerpt (default: 200)\n * @returns Truncated excerpt with ellipsis if needed\n */\nexport function createExcerpt(text: string, maxLength: number = 200): string {\n  // Remove markdown syntax for cleaner excerpt\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  if (cleanText.length <= maxLength) {\n    return cleanText\n  }\n\n  // Find the last complete word within the limit\n  const truncated = cleanText.substring(0, maxLength)\n  const lastSpaceIndex = truncated.lastIndexOf(' ')\n  \n  if (lastSpaceIndex > 0) {\n    return truncated.substring(0, lastSpaceIndex) + '...'\n  }\n  \n  return truncated + '...'\n}\n\n/**\n * Search through posts by title and content\n * @param posts - Array of posts to search through\n * @param query - Search query string\n * @returns Filtered array of posts matching the query\n */\nexport function searchPosts<T extends { title: string; content: string }>(\n  posts: T[],\n  query: string\n): T[] {\n  if (!query.trim()) {\n    return posts\n  }\n\n  const searchTerm = query.toLowerCase().trim()\n  \n  return posts.filter(post => {\n    const titleMatch = post.title.toLowerCase().includes(searchTerm)\n    const contentMatch = post.content.toLowerCase().includes(searchTerm)\n    return titleMatch || contentMatch\n  })\n}\n\n/**\n * Paginate an array of items\n * @param items - Array of items to paginate\n * @param page - Current page number (1-based)\n * @param itemsPerPage - Number of items per page\n * @returns Object with paginated items and pagination info\n */\nexport function paginateItems<T>(\n  items: T[],\n  page: number,\n  itemsPerPage: number\n): {\n  items: T[]\n  totalItems: number\n  totalPages: number\n  currentPage: number\n  hasNextPage: boolean\n  hasPreviousPage: boolean\n} {\n  const totalItems = items.length\n  const totalPages = Math.ceil(totalItems / itemsPerPage) || 1\n\n  // Use the page as provided (validation should be done by caller)\n  const currentPage = page\n  const startIndex = (currentPage - 1) * itemsPerPage\n  const endIndex = startIndex + itemsPerPage\n\n  return {\n    items: items.slice(startIndex, endIndex),\n    totalItems,\n    totalPages,\n    currentPage,\n    hasNextPage: currentPage < totalPages,\n    hasPreviousPage: currentPage > 1\n  }\n}\n\n/**\n * Format a date for display\n * @param date - Date string or Date object\n * @param options - Intl.DateTimeFormat options\n * @returns Formatted date string\n */\nexport function formatDate(\n  date: string | Date,\n  options: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }\n): string {\n  return new Date(date).toLocaleDateString('en-US', options)\n}\n\n/**\n * Get relative time string (e.g., \"2 days ago\")\n * @param date - Date string or Date object\n * @returns Relative time string\n */\nexport function getRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return 'just now'\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,qBAAqB,IAAY,EAAE,iBAAyB,GAAG;IAC7E,oEAAoE;IACpE,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,MAAM,YAAY,UAAU,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAC7E,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY;IAE1C,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa,mBAAmB;;AACrD;AAQO,SAAS,cAAc,IAAY,EAAE,YAAoB,GAAG;IACjE,6CAA6C;IAC7C,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,IAAI,UAAU,MAAM,IAAI,WAAW;QACjC,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,YAAY,UAAU,SAAS,CAAC,GAAG;IACzC,MAAM,iBAAiB,UAAU,WAAW,CAAC;IAE7C,IAAI,iBAAiB,GAAG;QACtB,OAAO,UAAU,SAAS,CAAC,GAAG,kBAAkB;IAClD;IAEA,OAAO,YAAY;AACrB;AAQO,SAAS,YACd,KAAU,EACV,KAAa;IAEb,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QACrD,MAAM,eAAe,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;QACzD,OAAO,cAAc;IACvB;AACF;AASO,SAAS,cACd,KAAU,EACV,IAAY,EACZ,YAAoB;IASpB,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa,iBAAiB;IAE3D,iEAAiE;IACjE,MAAM,cAAc;IACpB,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAE9B,OAAO;QACL,OAAO,MAAM,KAAK,CAAC,YAAY;QAC/B;QACA;QACA;QACA,aAAa,cAAc;QAC3B,iBAAiB,cAAc;IACjC;AACF;AAQO,SAAS,WACd,IAAmB,EACnB,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;AACpD;AAOO,SAAS,gBAAgB,IAAmB;IACjD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,kBAAkB,IAAI,KAAK,IAAI,IAAI,CAAC;IACvE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC;IAC9D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,MAAM,EAAE,iBAAiB,IAAI,KAAK,IAAI,IAAI,CAAC;IACpE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;AACjE", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/skeleton.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface SkeletonProps {\n  className?: string\n}\n\nexport function Skeleton({ className }: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        \"animate-pulse rounded-md bg-muted\",\n        className\n      )}\n    />\n  )\n}\n\nexport function PostCardSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn(\n      \"bg-card rounded-xl shadow-sm border border-border p-6 lg:p-8\",\n      className\n    )}>\n      <div className=\"space-y-4\">\n        {/* Title skeleton */}\n        <Skeleton className=\"h-7 w-3/4\" />\n        \n        {/* Content skeleton - multiple lines */}\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-2/3\" />\n        </div>\n        \n        {/* Footer skeleton */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2\">\n          <div className=\"flex items-center gap-2\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-32\" />\n          </div>\n          <Skeleton className=\"h-4 w-20\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function PostGridSkeleton({ count = 6 }: { count?: number }) {\n  return (\n    <div className=\"grid gap-6 lg:gap-8 md:grid-cols-2 lg:grid-cols-3\">\n      {Array.from({ length: count }).map((_, index) => (\n        <PostCardSkeleton key={index} />\n      ))}\n    </div>\n  )\n}\n\nexport function HeroSkeleton() {\n  return (\n    <div className=\"text-center py-16 lg:py-24 space-y-6\">\n      <div className=\"space-y-4\">\n        <Skeleton className=\"h-12 w-2/3 mx-auto\" />\n        <Skeleton className=\"h-6 w-1/2 mx-auto\" />\n      </div>\n      <div className=\"space-y-3\">\n        <Skeleton className=\"h-4 w-3/4 mx-auto\" />\n        <Skeleton className=\"h-4 w-2/3 mx-auto\" />\n      </div>\n      <Skeleton className=\"h-12 w-40 mx-auto\" />\n    </div>\n  )\n}\n\nexport function SearchBarSkeleton() {\n  return (\n    <div className=\"relative\">\n      <Skeleton className=\"h-12 w-full rounded-lg\" />\n    </div>\n  )\n}\n\nexport function FeaturedPostSkeleton() {\n  return (\n    <div className=\"bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-6 lg:p-8 border border-primary/20\">\n      <div className=\"space-y-4\">\n        {/* Featured badge */}\n        <Skeleton className=\"h-6 w-20\" />\n        \n        {/* Title */}\n        <Skeleton className=\"h-8 w-4/5\" />\n        \n        {/* Content */}\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-3/4\" />\n        </div>\n        \n        {/* Footer */}\n        <div className=\"flex items-center justify-between pt-2\">\n          <div className=\"flex items-center gap-2\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-32\" />\n          </div>\n          <Skeleton className=\"h-4 w-24\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function FilterSkeleton() {\n  return (\n    <div className=\"flex flex-wrap gap-2\">\n      {Array.from({ length: 4 }).map((_, index) => (\n        <Skeleton key={index} className=\"h-8 w-20 rounded-full\" />\n      ))}\n    </div>\n  )\n}\n\nexport function PaginationSkeleton() {\n  return (\n    <div className=\"flex items-center justify-center gap-2\">\n      <Skeleton className=\"h-10 w-20\" />\n      {Array.from({ length: 5 }).map((_, index) => (\n        <Skeleton key={index} className=\"h-10 w-10\" />\n      ))}\n      <Skeleton className=\"h-10 w-20\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAFA;;;AAQO,SAAS,SAAS,EAAE,SAAS,EAAiB;IACnD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA;;;;;;AAIR;KATgB;AAWT,SAAS,iBAAiB,EAAE,SAAS,EAA0B;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gEACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAS,WAAU;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;8BAItB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAS,WAAU;;;;;;8CACpB,6LAAC;oCAAS,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;MA5BgB;AA8BT,SAAS,iBAAiB,EAAE,QAAQ,CAAC,EAAsB;IAChE,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC,sBAAsB;;;;;;;;;;AAI/B;MARgB;AAUT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAEtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAEtB,6LAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B;MAdgB;AAgBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAS,WAAU;;;;;;;;;;;AAG1B;MANgB;AAQT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAS,WAAU;;;;;;8BAGpB,6LAAC;oBAAS,WAAU;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;8BAItB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAS,WAAU;;;;;;8CACpB,6LAAC;oCAAS,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;MA5BgB;AA8BT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gBAAqB,WAAU;eAAjB;;;;;;;;;;AAIvB;MARgB;AAUT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAS,WAAU;;;;;;YACnB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;oBAAqB,WAAU;mBAAjB;;;;;0BAEjB,6LAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B;MAVgB", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/homepage-client-wrapper.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport dynamic from 'next/dynamic'\r\nimport { HeroSkeleton, PostGridSkeleton } from '@/components/skeleton'\r\nimport type { Post } from '@/types/database'\r\n\r\nconst HomePageClient = dynamic(\r\n  () => import('./homepage-client').then(mod => mod.HomePageClient),\r\n  {\r\n    ssr: false,\r\n    loading: () => (\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <HeroSkeleton />\r\n        <PostGridSkeleton />\r\n      </div>\r\n    ),\r\n  }\r\n)\r\n\r\ninterface HomePageClientWrapperProps {\r\n  initialPosts: Post[]\r\n  user: { id: string; email?: string } | null\r\n  userIsAdmin: boolean\r\n}\r\n\r\nexport default function HomePageClientWrapper(props: HomePageClientWrapperProps) {\r\n  return <HomePageClient {...props} />\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAC3B,IAAM,6JAA4B,IAAI,CAAC,CAAA,MAAO,IAAI,cAAc;;;;;;IAE9D,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;;8BACb,6LAAC,iIAAA,CAAA,eAAY;;;;;8BACb,6LAAC,iIAAA,CAAA,mBAAgB;;;;;;;;;;;;KAPnB;AAmBS,SAAS,sBAAsB,KAAiC;IAC7E,qBAAO,6LAAC;QAAgB,GAAG,KAAK;;;;;;AAClC;MAFwB", "debugId": null}}]}