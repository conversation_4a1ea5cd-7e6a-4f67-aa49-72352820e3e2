{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Calculate estimated reading time for a text\n * @param text - The text content to analyze\n * @param wordsPerMinute - Average reading speed (default: 200 words per minute)\n * @returns Estimated reading time in minutes\n */\nexport function calculateReadingTime(text: string, wordsPerMinute: number = 200): number {\n  // Remove markdown syntax and HTML tags for more accurate word count\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  const wordCount = cleanText.split(' ').filter(word => word.length > 0).length\n  const readingTime = Math.ceil(wordCount / wordsPerMinute)\n  \n  return Math.max(1, readingTime) // Minimum 1 minute\n}\n\n/**\n * Create an excerpt from text content\n * @param text - The full text content\n * @param maxLength - Maximum length of the excerpt (default: 200)\n * @returns Truncated excerpt with ellipsis if needed\n */\nexport function createExcerpt(text: string, maxLength: number = 200): string {\n  // Remove markdown syntax for cleaner excerpt\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  if (cleanText.length <= maxLength) {\n    return cleanText\n  }\n\n  // Find the last complete word within the limit\n  const truncated = cleanText.substring(0, maxLength)\n  const lastSpaceIndex = truncated.lastIndexOf(' ')\n  \n  if (lastSpaceIndex > 0) {\n    return truncated.substring(0, lastSpaceIndex) + '...'\n  }\n  \n  return truncated + '...'\n}\n\n/**\n * Search through posts by title and content\n * @param posts - Array of posts to search through\n * @param query - Search query string\n * @returns Filtered array of posts matching the query\n */\nexport function searchPosts<T extends { title: string; content: string }>(\n  posts: T[],\n  query: string\n): T[] {\n  if (!query.trim()) {\n    return posts\n  }\n\n  const searchTerm = query.toLowerCase().trim()\n  \n  return posts.filter(post => {\n    const titleMatch = post.title.toLowerCase().includes(searchTerm)\n    const contentMatch = post.content.toLowerCase().includes(searchTerm)\n    return titleMatch || contentMatch\n  })\n}\n\n/**\n * Paginate an array of items\n * @param items - Array of items to paginate\n * @param page - Current page number (1-based)\n * @param itemsPerPage - Number of items per page\n * @returns Object with paginated items and pagination info\n */\nexport function paginateItems<T>(\n  items: T[],\n  page: number,\n  itemsPerPage: number\n): {\n  items: T[]\n  totalItems: number\n  totalPages: number\n  currentPage: number\n  hasNextPage: boolean\n  hasPreviousPage: boolean\n} {\n  const totalItems = items.length\n  const totalPages = Math.ceil(totalItems / itemsPerPage) || 1\n\n  // Ensure page is within valid bounds\n  const currentPage = Math.max(1, Math.min(page, totalPages))\n  const startIndex = (currentPage - 1) * itemsPerPage\n  const endIndex = startIndex + itemsPerPage\n\n  return {\n    items: items.slice(startIndex, endIndex),\n    totalItems,\n    totalPages,\n    currentPage,\n    hasNextPage: currentPage < totalPages,\n    hasPreviousPage: currentPage > 1\n  }\n}\n\n/**\n * Format a date for display\n * @param date - Date string or Date object\n * @param options - Intl.DateTimeFormat options\n * @returns Formatted date string\n */\nexport function formatDate(\n  date: string | Date,\n  options: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }\n): string {\n  return new Date(date).toLocaleDateString('en-US', options)\n}\n\n/**\n * Get relative time string (e.g., \"2 days ago\")\n * @param date - Date string or Date object\n * @returns Relative time string\n */\nexport function getRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return 'just now'\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,qBAAqB,IAAY,EAAE,iBAAyB,GAAG;IAC7E,oEAAoE;IACpE,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,MAAM,YAAY,UAAU,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAC7E,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY;IAE1C,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa,mBAAmB;;AACrD;AAQO,SAAS,cAAc,IAAY,EAAE,YAAoB,GAAG;IACjE,6CAA6C;IAC7C,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,IAAI,UAAU,MAAM,IAAI,WAAW;QACjC,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,YAAY,UAAU,SAAS,CAAC,GAAG;IACzC,MAAM,iBAAiB,UAAU,WAAW,CAAC;IAE7C,IAAI,iBAAiB,GAAG;QACtB,OAAO,UAAU,SAAS,CAAC,GAAG,kBAAkB;IAClD;IAEA,OAAO,YAAY;AACrB;AAQO,SAAS,YACd,KAAU,EACV,KAAa;IAEb,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QACrD,MAAM,eAAe,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;QACzD,OAAO,cAAc;IACvB;AACF;AASO,SAAS,cACd,KAAU,EACV,IAAY,EACZ,YAAoB;IASpB,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa,iBAAiB;IAE3D,qCAAqC;IACrC,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;IAC/C,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAE9B,OAAO;QACL,OAAO,MAAM,KAAK,CAAC,YAAY;QAC/B;QACA;QACA;QACA,aAAa,cAAc;QAC3B,iBAAiB,cAAc;IACjC;AACF;AAQO,SAAS,WACd,IAAmB,EACnB,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;AACpD;AAOO,SAAS,gBAAgB,IAAmB;IACjD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,kBAAkB,IAAI,KAAK,IAAI,IAAI,CAAC;IACvE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC;IAC9D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,MAAM,EAAE,iBAAiB,IAAI,KAAK,IAAI,IAAI,CAAC;IACpE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;AACjE", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/pagination.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface PaginationProps {\n  currentPage: number\n  totalPages: number\n  onPageChange: (page: number) => void\n  className?: string\n  showFirstLast?: boolean\n  maxVisiblePages?: number\n}\n\nexport function Pagination({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className,\n  showFirstLast = true,\n  maxVisiblePages = 5\n}: PaginationProps) {\n  if (totalPages <= 1) return null\n\n  const getVisiblePages = () => {\n    const pages: (number | string)[] = []\n    \n    if (totalPages <= maxVisiblePages) {\n      // Show all pages if total is less than max visible\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i)\n      }\n    } else {\n      // Calculate start and end of visible range\n      let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))\n      const end = Math.min(totalPages, start + maxVisiblePages - 1)\n      \n      // Adjust start if we're near the end\n      if (end - start + 1 < maxVisiblePages) {\n        start = Math.max(1, end - maxVisiblePages + 1)\n      }\n      \n      // Add first page and ellipsis if needed\n      if (start > 1) {\n        pages.push(1)\n        if (start > 2) {\n          pages.push('...')\n        }\n      }\n      \n      // Add visible pages\n      for (let i = start; i <= end; i++) {\n        pages.push(i)\n      }\n      \n      // Add ellipsis and last page if needed\n      if (end < totalPages) {\n        if (end < totalPages - 1) {\n          pages.push('...')\n        }\n        pages.push(totalPages)\n      }\n    }\n    \n    return pages\n  }\n\n  const visiblePages = getVisiblePages()\n\n  const buttonClass = (isActive: boolean = false, isDisabled: boolean = false) =>\n    cn(\n      \"flex items-center justify-center\",\n      \"min-w-[40px] h-10 px-3\",\n      \"text-sm font-medium\",\n      \"border border-border rounded-lg\",\n      \"transition-all duration-200\",\n      \"focus:outline-none focus:ring-2 focus:ring-primary/20\",\n      isActive\n        ? \"bg-primary text-primary-foreground border-primary shadow-sm\"\n        : isDisabled\n        ? \"bg-muted text-muted-foreground cursor-not-allowed\"\n        : \"bg-background text-foreground hover:bg-muted hover:border-primary/30 hover:shadow-sm\"\n    )\n\n  return (\n    <nav \n      className={cn(\"flex items-center justify-center gap-1 sm:gap-2\", className)}\n      aria-label=\"Pagination\"\n    >\n      {/* First Page Button */}\n      {showFirstLast && currentPage > 1 && (\n        <button\n          onClick={() => onPageChange(1)}\n          className={buttonClass(false, false)}\n          aria-label=\"Go to first page\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7m8 14l-7-7 7-7\" />\n          </svg>\n        </button>\n      )}\n\n      {/* Previous Page Button */}\n      <button\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage <= 1}\n        className={buttonClass(false, currentPage <= 1)}\n        aria-label=\"Go to previous page\"\n      >\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n        </svg>\n        <span className=\"hidden sm:inline ml-1\">Previous</span>\n      </button>\n\n      {/* Page Numbers */}\n      <div className=\"flex items-center gap-1\">\n        {visiblePages.map((page, index) => (\n          <div key={index}>\n            {page === '...' ? (\n              <span className=\"flex items-center justify-center min-w-[40px] h-10 text-muted-foreground\">\n                ...\n              </span>\n            ) : (\n              <button\n                onClick={() => onPageChange(page as number)}\n                className={buttonClass(page === currentPage, false)}\n                aria-label={`Go to page ${page}`}\n                aria-current={page === currentPage ? 'page' : undefined}\n              >\n                {page}\n              </button>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Next Page Button */}\n      <button\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage >= totalPages}\n        className={buttonClass(false, currentPage >= totalPages)}\n        aria-label=\"Go to next page\"\n      >\n        <span className=\"hidden sm:inline mr-1\">Next</span>\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n        </svg>\n      </button>\n\n      {/* Last Page Button */}\n      {showFirstLast && currentPage < totalPages && (\n        <button\n          onClick={() => onPageChange(totalPages)}\n          className={buttonClass(false, false)}\n          aria-label=\"Go to last page\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 5l7 7-7 7M5 5l7 7-7 7\" />\n          </svg>\n        </button>\n      )}\n    </nav>\n  )\n}\n\ninterface PaginationInfoProps {\n  currentPage: number\n  totalPages: number\n  totalItems: number\n  itemsPerPage: number\n  className?: string\n}\n\nexport function PaginationInfo({\n  currentPage,\n  totalItems,\n  itemsPerPage,\n  className\n}: PaginationInfoProps) {\n  const startItem = (currentPage - 1) * itemsPerPage + 1\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems)\n\n  return (\n    <div className={cn(\n      \"flex items-center justify-center text-sm text-muted-foreground\",\n      className\n    )}>\n      Showing {startItem} to {endItem} of {totalItems} posts\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaO,SAAS,WAAW,EACzB,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACT,gBAAgB,IAAI,EACpB,kBAAkB,CAAC,EACH;IAChB,IAAI,cAAc,GAAG,OAAO;IAE5B,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,IAAI,cAAc,iBAAiB;YACjC,mDAAmD;YACnD,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;QACF,OAAO;YACL,2CAA2C;YAC3C,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,kBAAkB;YACnE,MAAM,MAAM,KAAK,GAAG,CAAC,YAAY,QAAQ,kBAAkB;YAE3D,qCAAqC;YACrC,IAAI,MAAM,QAAQ,IAAI,iBAAiB;gBACrC,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,kBAAkB;YAC9C;YAEA,wCAAwC;YACxC,IAAI,QAAQ,GAAG;gBACb,MAAM,IAAI,CAAC;gBACX,IAAI,QAAQ,GAAG;oBACb,MAAM,IAAI,CAAC;gBACb;YACF;YAEA,oBAAoB;YACpB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,MAAM,IAAI,CAAC;YACb;YAEA,uCAAuC;YACvC,IAAI,MAAM,YAAY;gBACpB,IAAI,MAAM,aAAa,GAAG;oBACxB,MAAM,IAAI,CAAC;gBACb;gBACA,MAAM,IAAI,CAAC;YACb;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,cAAc,CAAC,WAAoB,KAAK,EAAE,aAAsB,KAAK,GACzE,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACC,oCACA,0BACA,uBACA,mCACA,+BACA,yDACA,WACI,gEACA,aACA,sDACA;IAGR,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;QACjE,cAAW;;YAGV,iBAAiB,cAAc,mBAC9B,6LAAC;gBACC,SAAS,IAAM,aAAa;gBAC5B,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBACC,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;kCACE,SAAS,sBACR,6LAAC;4BAAK,WAAU;sCAA2E;;;;;iDAI3F,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,YAAY,SAAS,aAAa;4BAC7C,cAAY,CAAC,WAAW,EAAE,MAAM;4BAChC,gBAAc,SAAS,cAAc,SAAS;sCAE7C;;;;;;uBAZG;;;;;;;;;;0BAoBd,6LAAC;gBACC,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAKxE,iBAAiB,cAAc,4BAC9B,6LAAC;gBACC,SAAS,IAAM,aAAa;gBAC5B,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMjF;KAtJgB;AAgKT,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACW;IACpB,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,eAAe;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kEACA;;YACC;YACQ;YAAU;YAAK;YAAQ;YAAK;YAAW;;;;;;;AAGtD;MAjBgB", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/pagination-test.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useMemo } from 'react'\r\nimport { Pagination, PaginationInfo } from '@/components/pagination'\r\nimport { paginateItems } from '@/lib/utils'\r\n\r\n// Generate sample posts for testing\r\nconst generateSamplePosts = (count: number) => {\r\n  return Array.from({ length: count }, (_, i) => ({\r\n    id: `post-${i + 1}`,\r\n    title: `Sample Post ${i + 1}`,\r\n    content: `This is the content for sample post ${i + 1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,\r\n    created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),\r\n    author_id: 'test-author'\r\n  }))\r\n}\r\n\r\nconst POSTS_PER_PAGE = 6\r\n\r\nexport function PaginationTest() {\r\n  const [currentPage, setCurrentPage] = useState(1)\r\n  \r\n  // Generate 15 sample posts to test pagination\r\n  const samplePosts = useMemo(() => generateSamplePosts(15), [])\r\n  \r\n  // Paginate the sample posts\r\n  const paginatedData = useMemo(() => {\r\n    return paginateItems(samplePosts, currentPage, POSTS_PER_PAGE)\r\n  }, [samplePosts, currentPage])\r\n\r\n  // Handle page change with validation\r\n  const handlePageChange = (page: number) => {\r\n    const totalPages = Math.ceil(samplePosts.length / POSTS_PER_PAGE) || 1\r\n    const validPage = Math.max(1, Math.min(page, totalPages))\r\n    console.log('Pagination Test - Page change:', {\r\n      requestedPage: page,\r\n      validPage,\r\n      currentPage,\r\n      totalPages,\r\n      totalPosts: samplePosts.length\r\n    })\r\n    setCurrentPage(validPage)\r\n  }\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto p-6\">\r\n      <h1 className=\"text-2xl font-bold mb-6\">Pagination Test</h1>\r\n      \r\n      <div className=\"mb-6\">\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Testing with {samplePosts.length} sample posts, {POSTS_PER_PAGE} per page\r\n        </p>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Current page: {currentPage}, Total pages: {paginatedData.totalPages}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Sample Posts Display */}\r\n      <div className=\"grid gap-4 mb-8\">\r\n        {paginatedData.items.map((post) => (\r\n          <div key={post.id} className=\"p-4 border border-border rounded-lg bg-card\">\r\n            <h3 className=\"font-semibold\">{post.title}</h3>\r\n            <p className=\"text-sm text-muted-foreground mt-1\">{post.content}</p>\r\n            <p className=\"text-xs text-muted-foreground mt-2\">\r\n              Created: {new Date(post.created_at).toLocaleDateString()}\r\n            </p>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Pagination Controls */}\r\n      {paginatedData.totalPages > 1 && (\r\n        <div className=\"space-y-4\">\r\n          <Pagination\r\n            currentPage={paginatedData.currentPage}\r\n            totalPages={paginatedData.totalPages}\r\n            onPageChange={handlePageChange}\r\n          />\r\n          <PaginationInfo\r\n            currentPage={paginatedData.currentPage}\r\n            totalPages={paginatedData.totalPages}\r\n            totalItems={paginatedData.totalItems}\r\n            itemsPerPage={POSTS_PER_PAGE}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Debug Info */}\r\n      <div className=\"mt-8 p-4 bg-muted rounded-lg\">\r\n        <h3 className=\"font-semibold mb-2\">Debug Info:</h3>\r\n        <pre className=\"text-xs\">\r\n          {JSON.stringify({\r\n            currentPage,\r\n            totalPages: paginatedData.totalPages,\r\n            totalItems: paginatedData.totalItems,\r\n            itemsOnCurrentPage: paginatedData.items.length,\r\n            hasNextPage: paginatedData.hasNextPage,\r\n            hasPreviousPage: paginatedData.hasPreviousPage\r\n          }, null, 2)}\r\n        </pre>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,oCAAoC;AACpC,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG,IAAM,CAAC;YAC9C,IAAI,CAAC,KAAK,EAAE,IAAI,GAAG;YACnB,OAAO,CAAC,YAAY,EAAE,IAAI,GAAG;YAC7B,SAAS,CAAC,oCAAoC,EAAE,IAAI,EAAE,0DAA0D,CAAC;YACjH,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;YACtE,WAAW;QACb,CAAC;AACH;AAEA,MAAM,iBAAiB;AAEhB,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE,IAAM,oBAAoB;8CAAK,EAAE;IAE7D,4BAA4B;IAC5B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,OAAO,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,aAAa;QACjD;gDAAG;QAAC;QAAa;KAAY;IAE7B,qCAAqC;IACrC,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,KAAK,IAAI,CAAC,YAAY,MAAM,GAAG,mBAAmB;QACrE,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;QAC7C,QAAQ,GAAG,CAAC,kCAAkC;YAC5C,eAAe;YACf;YACA;YACA;YACA,YAAY,YAAY,MAAM;QAChC;QACA,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;4BAAgC;4BAC7B,YAAY,MAAM;4BAAC;4BAAgB;4BAAe;;;;;;;kCAElE,6LAAC;wBAAE,WAAU;;4BAAgC;4BAC5B;4BAAY;4BAAgB,cAAc,UAAU;;;;;;;;;;;;;0BAKvE,6LAAC;gBAAI,WAAU;0BACZ,cAAc,KAAK,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;wBAAkB,WAAU;;0CAC3B,6LAAC;gCAAG,WAAU;0CAAiB,KAAK,KAAK;;;;;;0CACzC,6LAAC;gCAAE,WAAU;0CAAsC,KAAK,OAAO;;;;;;0CAC/D,6LAAC;gCAAE,WAAU;;oCAAqC;oCACtC,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;uBAJhD,KAAK,EAAE;;;;;;;;;;YAWpB,cAAc,UAAU,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,aAAU;wBACT,aAAa,cAAc,WAAW;wBACtC,YAAY,cAAc,UAAU;wBACpC,cAAc;;;;;;kCAEhB,6LAAC,mIAAA,CAAA,iBAAc;wBACb,aAAa,cAAc,WAAW;wBACtC,YAAY,cAAc,UAAU;wBACpC,YAAY,cAAc,UAAU;wBACpC,cAAc;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC;4BACd;4BACA,YAAY,cAAc,UAAU;4BACpC,YAAY,cAAc,UAAU;4BACpC,oBAAoB,cAAc,KAAK,CAAC,MAAM;4BAC9C,aAAa,cAAc,WAAW;4BACtC,iBAAiB,cAAc,eAAe;wBAChD,GAAG,MAAM;;;;;;;;;;;;;;;;;;AAKnB;GApFgB;KAAA", "debugId": null}}]}