{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-card.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Post } from '@/types/database'\nimport { calculateReadingTime, createExcerpt, formatDate, cn } from '@/lib/utils'\n\ninterface PostCardProps {\n  post: Post\n  featured?: boolean\n  className?: string\n  showExcerpt?: boolean\n  excerptLength?: number\n  style?: React.CSSProperties\n}\n\nexport function PostCard({ \n  post, \n  featured = false, \n  className,\n  showExcerpt = true,\n  excerptLength = 200\n}: PostCardProps) {\n  const readingTime = calculateReadingTime(post.content)\n  const excerpt = showExcerpt ? createExcerpt(post.content, excerptLength) : ''\n  const isRecent = new Date(post.created_at) > new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // Within last 1 day\n\n  if (featured) {\n    return (\n      <article className={cn(\n        \"group relative overflow-hidden\",\n        \"bg-gradient-to-br from-primary/5 via-primary/3 to-background\",\n        \"rounded-2xl border border-primary/20 shadow-lg\",\n        \"hover:shadow-xl hover:border-primary/30\",\n        \"transition-all duration-300\",\n        \"p-6 lg:p-8\",\n        className\n      )}>\n        {/* Featured Badge */}\n        <div className=\"absolute top-4 right-4\" suppressHydrationWarning>\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary text-primary-foreground shadow-sm\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n          </span>\n        </div>\n\n        <div className=\"space-y-4\" suppressHydrationWarning>\n          <div suppressHydrationWarning>\n            <h2 className=\"text-2xl lg:text-3xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors leading-tight\">\n              <Link href={`/posts/${post.id}`} className=\"block\">\n                {post.title}\n              </Link>\n            </h2>\n            \n            {showExcerpt && (\n              <p className=\"text-muted-foreground leading-relaxed text-base lg:text-lg\">\n                {excerpt}\n              </p>\n            )}\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2\" suppressHydrationWarning>\n            <div className=\"flex items-center gap-4 text-sm text-muted-foreground\" suppressHydrationWarning>\n              <time className=\"flex items-center gap-1\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                {formatDate(post.created_at)}\n              </time>\n              \n              <span className=\"flex items-center gap-1\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                {readingTime} min read\n              </span>\n            </div>\n\n            <Link\n              href={`/posts/${post.id}`}\n              className=\"inline-flex items-center text-primary hover:text-primary/80 font-medium transition-all duration-200 group/link text-sm\"\n            >\n              Read full article\n              <svg className=\"w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </article>\n    )\n  }\n\n  return (\n    <article className={cn(\n      \"group relative overflow-hidden\",\n      \"bg-card rounded-xl shadow-sm border border-border\",\n      \"hover:shadow-xl hover:border-primary/30 hover:-translate-y-1\",\n      \"transition-all duration-300 ease-out\",\n      \"p-6\",\n      className\n    )}>\n      {/* New Post Indicator */}\n      {isRecent && (\n        <div className=\"absolute top-6 right-6 z-10\" suppressHydrationWarning>\n          <span className=\"inline-flex items-center px-3.5 py-1.5 rounded-full text-xs font-bold bg-gradient-to-br from-green-400 to-emerald-600 text-white shadow-xl transform hover:scale-105 transition-all duration-300 ease-out\">\n            <svg className=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            New\n          </span>\n        </div>\n      )}\n\n      {/* Gradient overlay on hover */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\" suppressHydrationWarning />\n\n      <div className=\"relative space-y-4\" suppressHydrationWarning>\n        <div suppressHydrationWarning>\n          <h3 className=\"text-xl font-semibold text-card-foreground mb-3 group-hover:text-primary transition-colors leading-tight line-clamp-2 pr-20\">\n            <Link href={`/posts/${post.id}`} className=\"block\">\n              {post.title}\n            </Link>\n          </h3>\n\n          {showExcerpt && (\n            <p className=\"text-muted-foreground leading-relaxed line-clamp-3 text-sm\">\n              {excerpt}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-4 border-t border-border/50\" suppressHydrationWarning>\n          <div className=\"flex items-center gap-4\" suppressHydrationWarning>\n            <time className=\"flex items-center gap-2 text-sm text-muted-foreground\" title={formatDate(post.created_at)}>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n              {formatDate(post.created_at)}\n            </time>\n\n            <span className=\"inline-flex items-center gap-1 px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium\">\n              <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              {readingTime}\n            </span>\n          </div>\n\n          <Link\n            href={`/posts/${post.id}`}\n            className=\"group inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-all duration-200\"\n          >\n            Read more\n            <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </article>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAWO,SAAS,SAAS,EACvB,IAAI,EACJ,WAAW,KAAK,EAChB,SAAS,EACT,cAAc,IAAI,EAClB,gBAAgB,GAAG,EACL;IACd,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,OAAO;IACrD,MAAM,UAAU,cAAc,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,OAAO,EAAE,iBAAiB;IAC3E,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,oBAAoB;;IAEhH,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAQ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,kCACA,gEACA,kDACA,2CACA,+BACA,cACA;;8BAGA,6LAAC;oBAAI,WAAU;oBAAyB,wBAAwB;8BAC9D,cAAA,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8BAK3E,6LAAC;oBAAI,WAAU;oBAAY,wBAAwB;;sCACjD,6LAAC;4BAAI,wBAAwB;;8CAC3B,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wCAAE,WAAU;kDACxC,KAAK,KAAK;;;;;;;;;;;gCAId,6BACC,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;4BAA0E,wBAAwB;;8CAC/G,6LAAC;oCAAI,WAAU;oCAAwD,wBAAwB;;sDAC7F,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;sDAG7B,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE;gDAAY;;;;;;;;;;;;;8CAIjB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oCACzB,WAAU;;wCACX;sDAEC,6LAAC;4CAAI,WAAU;4CAAmE,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC1H,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnF;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,kCACA,qDACA,gEACA,wCACA,OACA;;YAGC,0BACC,6LAAC;gBAAI,WAAU;gBAA8B,wBAAwB;0BACnE,cAAA,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;4BAAe,MAAK;4BAAe,SAAQ;sCACxD,cAAA,6LAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAwI,UAAS;;;;;;;;;;;wBACxK;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAU;gBAAgJ,wBAAwB;;;;;;0BAEvL,6LAAC;gBAAI,WAAU;gBAAqB,wBAAwB;;kCAC1D,6LAAC;wBAAI,wBAAwB;;0CAC3B,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oCAAE,WAAU;8CACxC,KAAK,KAAK;;;;;;;;;;;4BAId,6BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAKP,6LAAC;wBAAI,WAAU;wBAAoG,wBAAwB;;0CACzI,6LAAC;gCAAI,WAAU;gCAA0B,wBAAwB;;kDAC/D,6LAAC;wCAAK,WAAU;wCAAwD,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;0DACvG,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;kDAG7B,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE;;;;;;;;;;;;;0CAIL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gCACzB,WAAU;;oCACX;kDAEC,6LAAC;wCAAI,WAAU;wCAAsE,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7H,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;KAnJgB", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/search-bar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void\n  placeholder?: string\n  className?: string\n  autoFocus?: boolean\n}\n\nexport function SearchBar({ \n  onSearch, \n  placeholder = \"Search posts...\", \n  className,\n  autoFocus = false \n}: SearchBarProps) {\n  const [query, setQuery] = useState('')\n  const [isFocused, setIsFocused] = useState(false)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  useEffect(() => {\n    if (autoFocus && inputRef.current) {\n      inputRef.current.focus()\n    }\n  }, [autoFocus])\n\n  useEffect(() => {\n    // Debounce search to avoid too many calls\n    const timeoutId = setTimeout(() => {\n      onSearch(query)\n    }, 300)\n\n    return () => clearTimeout(timeoutId)\n  }, [query, onSearch])\n\n  const handleClear = () => {\n    setQuery('')\n    if (inputRef.current) {\n      inputRef.current.focus()\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClear()\n    }\n  }\n\n  return (\n    <div className={cn(\n      \"relative group\",\n      className\n    )} suppressHydrationWarning>\n      <div className={cn(\n        \"relative flex items-center transition-all duration-200\",\n        \"bg-background border border-border rounded-lg\",\n        \"hover:border-primary/30 focus-within:border-primary/50\",\n        \"shadow-sm hover:shadow-md focus-within:shadow-md\",\n        isFocused && \"ring-2 ring-primary/20\"\n      )} suppressHydrationWarning>\n        {/* Search Icon */}\n        <div className=\"absolute left-3 flex items-center pointer-events-none\" suppressHydrationWarning>\n          <svg \n            className={cn(\n              \"w-4 h-4 transition-colors duration-200\",\n              isFocused || query ? \"text-primary\" : \"text-muted-foreground\"\n            )} \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" \n            />\n          </svg>\n        </div>\n\n        {/* Input Field */}\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => setIsFocused(false)}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          className={cn(\n            \"w-full pl-10 pr-10 py-2.5 sm:py-3\",\n            \"bg-transparent border-0 outline-none\",\n            \"text-foreground placeholder:text-muted-foreground\",\n            \"text-sm sm:text-base\"\n          )}\n        />\n\n        {/* Clear Button */}\n        {query && (\n          <button\n            onClick={handleClear}\n            className={cn(\n              \"absolute right-3 flex items-center justify-center\",\n              \"w-5 h-5 rounded-full\",\n              \"text-muted-foreground hover:text-foreground\",\n              \"hover:bg-muted transition-all duration-200\",\n              \"focus:outline-none focus:ring-2 focus:ring-primary/20\"\n            )}\n            aria-label=\"Clear search\"\n          >\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Search Results Indicator */}\n      {query && (\n        <div className=\"absolute top-full left-0 right-0 mt-1\">\n          <div className=\"text-xs text-muted-foreground px-3 py-1\">\n            Searching for &quot;{query}&quot;...\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\ninterface SearchResultsProps {\n  query: string\n  totalResults: number\n  className?: string\n}\n\nexport function SearchResults({ query, totalResults, className }: SearchResultsProps) {\n  if (!query) return null\n\n  return (\n    <div className={cn(\n      \"flex items-center justify-between\",\n      \"px-4 py-2 mb-6\",\n      \"bg-muted/50 rounded-lg border border-border\",\n      className\n    )}>\n      <div className=\"flex items-center gap-2\">\n        <svg className=\"w-4 h-4 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n        </svg>\n        <span className=\"text-sm text-foreground\">\n          {totalResults === 0 ? (\n            <>No results found for <strong>&quot;{query}&quot;</strong></>\n          ) : (\n            <>\n              {totalResults} result{totalResults === 1 ? '' : 's'} for <strong>&quot;{query}&quot;</strong>\n            </>\n          )}\n        </span>\n      </div>\n      \n      {totalResults === 0 && (\n        <div className=\"text-xs text-muted-foreground\">\n          Try different keywords\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,iBAAiB,EAC/B,SAAS,EACT,YAAY,KAAK,EACF;;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,aAAa,SAAS,OAAO,EAAE;gBACjC,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;8BAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,0CAA0C;YAC1C,MAAM,YAAY;iDAAW;oBAC3B,SAAS;gBACX;gDAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAO;KAAS;IAEpB,MAAM,cAAc;QAClB,SAAS;QACT,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kBACA;QACC,wBAAwB;;0BACzB,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,0DACA,iDACA,0DACA,oDACA,aAAa;gBACZ,wBAAwB;;kCAEzB,6LAAC;wBAAI,WAAU;wBAAwD,wBAAwB;kCAC7F,cAAA,6LAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,aAAa,QAAQ,iBAAiB;4BAExC,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAMR,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,aAAa;wBAC5B,QAAQ,IAAM,aAAa;wBAC3B,WAAW;wBACX,aAAa;wBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,wCACA,qDACA;;;;;;oBAKH,uBACC,6LAAC;wBACC,SAAS;wBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,wBACA,+CACA,8CACA;wBAEF,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;YAO5E,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAA0C;wBAClC;wBAAM;;;;;;;;;;;;;;;;;;AAMvC;GAtHgB;KAAA;AA8HT,SAAS,cAAc,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAsB;IAClF,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qCACA,kBACA,+CACA;;0BAEA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAgC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACvF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAK,WAAU;kCACb,iBAAiB,kBAChB;;gCAAE;8CAAqB,6LAAC;;wCAAO;wCAAO;wCAAM;;;;;;;;yDAE5C;;gCACG;gCAAa;gCAAQ,iBAAiB,IAAI,KAAK;gCAAI;8CAAK,6LAAC;;wCAAO;wCAAO;wCAAM;;;;;;;;;;;;;;;;;;;;YAMrF,iBAAiB,mBAChB,6LAAC;gBAAI,WAAU;0BAAgC;;;;;;;;;;;;AAMvD;MAhCgB", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/pagination.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface PaginationProps {\r\n  currentPage: number\r\n  totalPages: number\r\n  onPageChange: (page: number) => void\r\n  className?: string\r\n  showFirstLast?: boolean\r\n  maxVisiblePages?: number\r\n}\r\n\r\nexport function Pagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  className,\r\n  showFirstLast = true,\r\n  maxVisiblePages = 5\r\n}: PaginationProps) {\r\n  if (totalPages <= 1) return null\r\n\r\n  const getVisiblePages = () => {\r\n    const pages: (number | string)[] = []\r\n    \r\n    if (totalPages <= maxVisiblePages) {\r\n      // Show all pages if total is less than max visible\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i)\r\n      }\r\n    } else {\r\n      // Calculate start and end of visible range\r\n      let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))\r\n      const end = Math.min(totalPages, start + maxVisiblePages - 1)\r\n      \r\n      // Adjust start if we're near the end\r\n      if (end - start + 1 < maxVisiblePages) {\r\n        start = Math.max(1, end - maxVisiblePages + 1)\r\n      }\r\n      \r\n      // Add first page and ellipsis if needed\r\n      if (start > 1) {\r\n        pages.push(1)\r\n        if (start > 2) {\r\n          pages.push('...')\r\n        }\r\n      }\r\n      \r\n      // Add visible pages\r\n      for (let i = start; i <= end; i++) {\r\n        pages.push(i)\r\n      }\r\n      \r\n      // Add ellipsis and last page if needed\r\n      if (end < totalPages) {\r\n        if (end < totalPages - 1) {\r\n          pages.push('...')\r\n        }\r\n        pages.push(totalPages)\r\n      }\r\n    }\r\n    \r\n    return pages\r\n  }\r\n\r\n  const visiblePages = getVisiblePages()\r\n\r\n  const buttonClass = (isActive: boolean = false, isDisabled: boolean = false) =>\r\n    cn(\r\n      \"flex items-center justify-center\",\r\n      \"min-w-[40px] h-10 px-3\",\r\n      \"text-sm font-medium\",\r\n      \"border border-border rounded-lg\",\r\n      \"transition-all duration-200\",\r\n      \"focus:outline-none focus:ring-2 focus:ring-primary/20\",\r\n      isActive\r\n        ? \"bg-primary text-primary-foreground border-primary shadow-sm\"\r\n        : isDisabled\r\n        ? \"bg-muted text-muted-foreground cursor-not-allowed\"\r\n        : \"bg-background text-foreground hover:bg-muted hover:border-primary/30 hover:shadow-sm\"\r\n    )\r\n\r\n  return (\r\n    <nav \r\n      className={cn(\"flex items-center justify-center gap-1 sm:gap-2\", className)}\r\n      aria-label=\"Pagination\"\r\n    >\r\n      {/* First Page Button */}\r\n      {showFirstLast && currentPage > 1 && (\r\n        <button\r\n          type=\"button\"\r\n          onClick={(e) => {\r\n            e.preventDefault();\r\n            e.stopPropagation();\r\n            onPageChange(1);\r\n          }}\r\n          className={buttonClass(false, false)}\r\n          aria-label=\"Go to first page\"\r\n        >\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7m8 14l-7-7 7-7\" />\r\n          </svg>\r\n        </button>\r\n      )}\r\n\r\n      {/* Previous Page Button */}\r\n      <button\r\n        type=\"button\"\r\n        onClick={(e) => {\r\n          e.preventDefault();\r\n          e.stopPropagation();\r\n          onPageChange(currentPage - 1);\r\n        }}\r\n        disabled={currentPage <= 1}\r\n        className={buttonClass(false, currentPage <= 1)}\r\n        aria-label=\"Go to previous page\"\r\n      >\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\r\n        </svg>\r\n        <span className=\"hidden sm:inline ml-1\">Previous</span>\r\n      </button>\r\n\r\n      {/* Page Numbers */}\r\n      <div className=\"flex items-center gap-1\">\r\n        {visiblePages.map((page, index) => (\r\n          <div key={index}>\r\n            {page === '...' ? (\r\n              <span className=\"flex items-center justify-center min-w-[40px] h-10 text-muted-foreground\">\r\n                ...\r\n              </span>\r\n            ) : (\r\n              <button\r\n                type=\"button\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  onPageChange(page as number);\r\n                }}\r\n                className={buttonClass(page === currentPage, false)}\r\n                aria-label={`Go to page ${page}`}\r\n                aria-current={page === currentPage ? 'page' : undefined}\r\n              >\r\n                {page}\r\n              </button>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Next Page Button */}\r\n      <button\r\n        type=\"button\"\r\n        onClick={(e) => {\r\n          e.preventDefault();\r\n          e.stopPropagation();\r\n          console.log('Next button clicked, current page:', currentPage, 'going to:', currentPage + 1);\r\n          onPageChange(currentPage + 1);\r\n        }}\r\n        disabled={currentPage >= totalPages}\r\n        className={buttonClass(false, currentPage >= totalPages)}\r\n        aria-label=\"Go to next page\"\r\n      >\r\n        <span className=\"hidden sm:inline mr-1\">Next</span>\r\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\r\n        </svg>\r\n      </button>\r\n\r\n      {/* Last Page Button */}\r\n      {showFirstLast && currentPage < totalPages && (\r\n        <button\r\n          type=\"button\"\r\n          onClick={(e) => {\r\n            e.preventDefault();\r\n            e.stopPropagation();\r\n            onPageChange(totalPages);\r\n          }}\r\n          className={buttonClass(false, false)}\r\n          aria-label=\"Go to last page\"\r\n        >\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 5l7 7-7 7M5 5l7 7-7 7\" />\r\n          </svg>\r\n        </button>\r\n      )}\r\n    </nav>\r\n  )\r\n}\r\n\r\ninterface PaginationInfoProps {\r\n  currentPage: number\r\n  totalPages: number\r\n  totalItems: number\r\n  itemsPerPage: number\r\n  className?: string\r\n}\r\n\r\nexport function PaginationInfo({\r\n  currentPage,\r\n  totalItems,\r\n  itemsPerPage,\r\n  className\r\n}: PaginationInfoProps) {\r\n  const startItem = (currentPage - 1) * itemsPerPage + 1\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems)\r\n\r\n  return (\r\n    <div className={cn(\r\n      \"flex items-center justify-center text-sm text-muted-foreground\",\r\n      className\r\n    )}>\r\n      Showing {startItem} to {endItem} of {totalItems} posts\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaO,SAAS,WAAW,EACzB,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACT,gBAAgB,IAAI,EACpB,kBAAkB,CAAC,EACH;IAChB,IAAI,cAAc,GAAG,OAAO;IAE5B,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,IAAI,cAAc,iBAAiB;YACjC,mDAAmD;YACnD,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;QACF,OAAO;YACL,2CAA2C;YAC3C,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,kBAAkB;YACnE,MAAM,MAAM,KAAK,GAAG,CAAC,YAAY,QAAQ,kBAAkB;YAE3D,qCAAqC;YACrC,IAAI,MAAM,QAAQ,IAAI,iBAAiB;gBACrC,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,kBAAkB;YAC9C;YAEA,wCAAwC;YACxC,IAAI,QAAQ,GAAG;gBACb,MAAM,IAAI,CAAC;gBACX,IAAI,QAAQ,GAAG;oBACb,MAAM,IAAI,CAAC;gBACb;YACF;YAEA,oBAAoB;YACpB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,MAAM,IAAI,CAAC;YACb;YAEA,uCAAuC;YACvC,IAAI,MAAM,YAAY;gBACpB,IAAI,MAAM,aAAa,GAAG;oBACxB,MAAM,IAAI,CAAC;gBACb;gBACA,MAAM,IAAI,CAAC;YACb;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,cAAc,CAAC,WAAoB,KAAK,EAAE,aAAsB,KAAK,GACzE,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACC,oCACA,0BACA,uBACA,mCACA,+BACA,yDACA,WACI,gEACA,aACA,sDACA;IAGR,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;QACjE,cAAW;;YAGV,iBAAiB,cAAc,mBAC9B,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,aAAa;gBACf;gBACA,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,aAAa,cAAc;gBAC7B;gBACA,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;kCACE,SAAS,sBACR,6LAAC;4BAAK,WAAU;sCAA2E;;;;;iDAI3F,6LAAC;4BACC,MAAK;4BACL,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,EAAE,eAAe;gCACjB,aAAa;4BACf;4BACA,WAAW,YAAY,SAAS,aAAa;4BAC7C,cAAY,CAAC,WAAW,EAAE,MAAM;4BAChC,gBAAc,SAAS,cAAc,SAAS;sCAE7C;;;;;;uBAjBG;;;;;;;;;;0BAyBd,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,QAAQ,GAAG,CAAC,sCAAsC,aAAa,aAAa,cAAc;oBAC1F,aAAa,cAAc;gBAC7B;gBACA,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAKxE,iBAAiB,cAAc,4BAC9B,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,aAAa;gBACf;gBACA,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMjF;KAhLgB;AA0LT,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACW;IACpB,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,eAAe;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kEACA;;YACC;YACQ;YAAU;YAAK;YAAQ;YAAK;YAAW;;;;;;;AAGtD;MAjBgB", "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;;8BAChB,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,6LAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GA9BgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,6LAAC,wIAAA,CAAA,cAAW;;;;;AACrB;KAFgB", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/calendar-display.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useEffect } from 'react'\r\n\r\nexport function CalendarDisplay() {\r\n  const [currentDateTime, setCurrentDateTime] = useState(new Date())\r\n  const [mounted, setMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    // Set mounted to true to prevent hydration mismatch\r\n    setMounted(true)\r\n\r\n    const timer = setInterval(() => {\r\n      setCurrentDateTime(new Date())\r\n    }, 1000) // Update every second\r\n\r\n    return () => clearInterval(timer)\r\n  }, [])\r\n\r\n  const formatDate = (date: Date) => {\r\n    return date.toLocaleDateString('en-US', {\r\n      weekday: 'long',\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n    })\r\n  }\r\n\r\n  const formatTime = (date: Date) => {\r\n    return date.toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n      second: '2-digit',\r\n      hour12: true,\r\n    })\r\n  }\r\n\r\n  // Prevent hydration mismatch by showing consistent content until mounted\r\n  if (!mounted) {\r\n    // Use a fixed date for initial server render to prevent hydration mismatch\r\n    const staticDate = new Date('2024-01-01T12:00:00')\r\n    return (\r\n      <section className=\"mb-8\">\r\n        <div className=\"bg-card border border-primary/20 rounded-lg p-6 shadow-xl text-center\">\r\n          <h2 className=\"text-xl font-bold text-foreground mb-4 flex items-center justify-center\">\r\n            <svg className=\"w-6 h-6 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n            </svg>\r\n            Today's Date & Time\r\n          </h2>\r\n          <p className=\"text-2xl font-semibold text-foreground mb-2\">\r\n            {formatDate(staticDate)}\r\n          </p>\r\n          <p className=\"text-3xl font-extrabold text-primary\">\r\n            {formatTime(staticDate)}\r\n          </p>\r\n        </div>\r\n      </section>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <section className=\"mb-8\">\r\n      <div className=\"bg-card border border-primary/20 rounded-lg p-6 shadow-xl text-center\">\r\n        <h2 className=\"text-xl font-bold text-foreground mb-4 flex items-center justify-center\">\r\n          <svg className=\"w-6 h-6 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n          </svg>\r\n          Today's Date & Time\r\n        </h2>\r\n        <p className=\"text-2xl font-semibold text-foreground mb-2\">\r\n          {formatDate(currentDateTime)}\r\n        </p>\r\n        <p className=\"text-3xl font-extrabold text-primary\">\r\n          {formatTime(currentDateTime)}\r\n        </p>\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIO,SAAS;;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,oDAAoD;YACpD,WAAW;YAEX,MAAM,QAAQ;mDAAY;oBACxB,mBAAmB,IAAI;gBACzB;kDAAG,MAAM,sBAAsB;;YAE/B;6CAAO,IAAM,cAAc;;QAC7B;oCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,yEAAyE;IACzE,IAAI,CAAC,SAAS;QACZ,2EAA2E;QAC3E,MAAM,aAAa,IAAI,KAAK;QAC5B,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAI,WAAU;gCAA4B,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACnF,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;kCAGR,6LAAC;wBAAE,WAAU;kCACV,WAAW;;;;;;kCAEd,6LAAC;wBAAE,WAAU;kCACV,WAAW;;;;;;;;;;;;;;;;;IAKtB;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC;4BAAI,WAAU;4BAA4B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACnF,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;wBACjE;;;;;;;8BAGR,6LAAC;oBAAE,WAAU;8BACV,WAAW;;;;;;8BAEd,6LAAC;oBAAE,WAAU;8BACV,WAAW;;;;;;;;;;;;;;;;;AAKtB;GA3EgB;KAAA", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/homepage-client.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useMemo, useEffect } from 'react'\r\nimport Link from 'next/link'\r\nimport { Post } from '@/types/database'\r\nimport { PostCard } from '@/components/post-card'\r\nimport { SearchBar, SearchResults } from '@/components/search-bar'\r\nimport { Pagination, PaginationInfo } from '@/components/pagination'\r\nimport { searchPosts, paginateItems, formatDate } from '@/lib/utils'\r\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\r\nimport { CalendarDisplay } from '@/components/calendar-display'\r\n\r\ninterface HomePageClientProps {\r\n  initialPosts: Post[]\r\n  user: { id: string; email?: string } | null\r\n  userIsAdmin: boolean\r\n}\r\n\r\nconst POSTS_PER_PAGE = 6\r\n\r\nexport function HomePageClient({ initialPosts: posts, user, userIsAdmin }: HomePageClientProps) {\r\n  const [searchQuery, setSearchQuery] = useState('')\r\n  const [currentPage, setCurrentPage] = useState(1)\r\n  const [isClient, setIsClient] = useState(false)\r\n\r\n  // Ensure we're on the client side to prevent hydration issues\r\n  useEffect(() => {\r\n    setIsClient(true)\r\n  }, [])\r\n\r\n  // Debug: Log state changes\r\n  console.log('HomePageClient render - currentPage:', currentPage, 'searchQuery:', searchQuery, 'isClient:', isClient)\r\n\r\n  // Debug: Track currentPage changes\r\n  useEffect(() => {\r\n    console.log('useEffect - currentPage changed to:', currentPage)\r\n  }, [currentPage])\r\n\r\n  // Filter posts based on search query and category\r\n  const filteredPosts = useMemo(() => {\r\n    return searchPosts(posts, searchQuery);\r\n  }, [posts, searchQuery])\r\n\r\n  // Paginate filtered posts\r\n  const paginatedData = useMemo(() => {\r\n    console.log('useMemo paginatedData - currentPage:', currentPage, 'filteredPosts.length:', filteredPosts.length);\r\n    const result = paginateItems(filteredPosts, currentPage, POSTS_PER_PAGE);\r\n    console.log('useMemo paginatedData - result:', result);\r\n\r\n    // If the current page is beyond the available pages due to filtering,\r\n    // we need to reset to the last available page (only on client side)\r\n    if (isClient && result.totalPages > 0 && currentPage > result.totalPages) {\r\n      console.log('useMemo - Page beyond available pages, resetting from', currentPage, 'to', result.totalPages);\r\n      // Use setTimeout to avoid state update during render\r\n      setTimeout(() => setCurrentPage(result.totalPages), 0);\r\n    }\r\n\r\n    return result;\r\n  }, [filteredPosts, currentPage])\r\n\r\n  // Reset to first page when search or category changes\r\n  const handleSearch = (query: string) => {\r\n    setSearchQuery(query)\r\n    setCurrentPage(1)\r\n  }\r\n\r\n  // Handle page change with validation\r\n  const handlePageChange = (page: number) => {\r\n    // Prevent state updates during hydration\r\n    if (!isClient) {\r\n      console.log('Homepage Pagination - Skipping page change during hydration');\r\n      return;\r\n    }\r\n\r\n    console.log('Homepage Pagination - handlePageChange called:', {\r\n      requestedPage: page,\r\n      currentPage,\r\n      totalPosts: filteredPosts.length,\r\n      postsPerPage: POSTS_PER_PAGE\r\n    });\r\n\r\n    const totalPages = Math.ceil(filteredPosts.length / POSTS_PER_PAGE) || 1;\r\n    const validPage = Math.max(1, Math.min(page, totalPages));\r\n\r\n    console.log('Homepage Pagination - Validation result:', {\r\n      totalPages,\r\n      validPage,\r\n      willUpdate: validPage !== currentPage\r\n    });\r\n\r\n    if (validPage !== currentPage) {\r\n      setCurrentPage(validPage);\r\n    }\r\n  }\r\n\r\n\r\n  // Get featured post (most recent post)\r\n  const featuredPost = posts.length > 0 ? posts[0] : null\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background\">\r\n      {/* Header */}\r\n      <header className=\"bg-card/95 shadow-lg border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\r\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <Link href=\"/\" className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground hover:text-primary transition-colors\">\r\n              My Blog\r\n            </Link>\r\n            <div className=\"flex items-center gap-1 sm:gap-2 lg:gap-4\">\r\n              <ClientThemeToggle />\r\n              {user ? (\r\n                <>\r\n                  <span className=\"hidden lg:block text-sm text-muted-foreground truncate max-w-32\">\r\n                    Welcome, {user.email}\r\n                  </span>\r\n                  \r\n                  {userIsAdmin && (\r\n                    <Link\r\n                      href=\"/admin/new-post\"\r\n                      className=\"bg-primary text-primary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\r\n                    >\r\n                      <span className=\"hidden sm:inline\">New Post</span>\r\n                      <span className=\"sm:hidden text-lg\">+</span>\r\n                    </Link>\r\n                  )}\r\n                  {userIsAdmin && (\r\n                    <Link\r\n                      href=\"/admin/manage-posts\"\r\n                      className=\"bg-secondary text-secondary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\r\n                    >\r\n                      <span className=\"hidden sm:inline\">Manage</span>\r\n                      <span className=\"sm:hidden text-lg\">⚙️</span>\r\n                    </Link>\r\n                  )}\r\n                  <form action=\"/auth/signout\" method=\"post\">\r\n                    <button\r\n                      type=\"submit\"\r\n                      className=\"inline-flex items-center justify-center px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md touch-manipulation min-h-[44px]\"\r\n                    >\r\n                      <svg className=\"w-4 h-4 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n                      </svg>\r\n                      <span className=\"hidden sm:inline\">Sign Out</span>\r\n                    </button>\r\n                  </form>\r\n                </>\r\n              ) : (\r\n                <Link\r\n                  href=\"/login\"\r\n                  className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\r\n                >\r\n                  Sign In\r\n                </Link>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16\">\r\n        {posts.length === 0 ? (\r\n          /* Empty State */\r\n          <div className=\"text-center py-16 lg:py-24\">\r\n            <div className=\"max-w-md mx-auto\">\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center\">\r\n                <svg className=\"w-8 h-8 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                </svg>\r\n              </div>\r\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">No posts yet</h2>\r\n              <p className=\"text-muted-foreground mb-6\">Start sharing your thoughts with the world!</p>\r\n              {userIsAdmin && (\r\n                <Link\r\n                  href=\"/admin/new-post\"\r\n                  className=\"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n                >\r\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\r\n                  </svg>\r\n                  Create your first post\r\n                </Link>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Main Content Layout with Sidebar */}\r\n            <div className=\"flex flex-col lg:flex-row gap-8 lg:gap-12\">\r\n              {/* Main Content Area */}\r\n              <div className=\"flex-1 lg:w-2/3 order-2 lg:order-1\">\r\n                {/* Search and Filter Section */}\r\n                <section id=\"posts\" className=\"mb-8 sm:mb-12\">\r\n                  <div className=\"flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8\">\r\n                    <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6\">\r\n                      <div className=\"text-center sm:text-left\">\r\n                        <h2 className=\"text-3xl sm:text-4xl font-extrabold text-foreground mb-2\">All Posts</h2>\r\n                        <p className=\"text-lg text-muted-foreground\">\r\n                          {posts.length} post{posts.length === 1 ? '' : 's'} available\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"w-full sm:max-w-xs\">\r\n                        <SearchBar onSearch={handleSearch} className=\"text-sm\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n\r\n                  {/* Search Results Info */}\r\n                  <SearchResults query={searchQuery} totalResults={filteredPosts.length} />\r\n                </section>\r\n\r\n                {/* Posts Grid */}\r\n                <section className=\"mb-16\">\r\n                  {paginatedData.items.length === 0 ? (\r\n                    <div className=\"text-center py-20\">\r\n                      <div className=\"w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-muted to-muted/50 rounded-2xl flex items-center justify-center\">\r\n                        <svg className=\"w-10 h-10 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <h3 className=\"text-2xl font-semibold text-foreground mb-3\">No posts found</h3>\r\n                      <p className=\"text-muted-foreground text-lg mb-6\">Try adjusting your search terms or browse all posts</p>\r\n                      <Link\r\n                        href=\"#posts\"\r\n                        onClick={() => {\r\n                          setSearchQuery('')\r\n                          handlePageChange(1)\r\n                        }}\r\n                        className=\"inline-flex items-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors\"\r\n                      >\r\n                        Clear Search\r\n                      </Link>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2\">\r\n                      {paginatedData.items\r\n                        .map((post, index) => (\r\n                        <PostCard\r\n                          key={post.id}\r\n                          post={post}\r\n                          className=\"animate-slide-up hover:scale-[1.02] transition-transform duration-300\"\r\n                          style={{\r\n                            animationDelay: `${index * 0.05}s`\r\n                          } as React.CSSProperties}\r\n                        />\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </section>\r\n\r\n                {/* Pagination */}\r\n                {paginatedData.totalPages > 1 && (\r\n                  <section className=\"mb-12 space-y-6\">\r\n                    <Pagination\r\n                      currentPage={paginatedData.currentPage}\r\n                      totalPages={paginatedData.totalPages}\r\n                      onPageChange={handlePageChange}\r\n                    />\r\n                    <PaginationInfo\r\n                      currentPage={paginatedData.currentPage}\r\n                      totalPages={paginatedData.totalPages}\r\n                      totalItems={paginatedData.totalItems}\r\n                      itemsPerPage={POSTS_PER_PAGE}\r\n                    />\r\n                  </section>\r\n                )}\r\n              </div>\r\n\r\n              {/* Right Sidebar */}\r\n              <aside className=\"lg:w-1/3 lg:max-w-sm order-1 lg:order-2\">\r\n                {/* Featured Post Section */}\r\n                <CalendarDisplay />\r\n\r\n                {/* Additional Sidebar Content - Recent Posts */}\r\n                {posts.length > 1 && (\r\n                  <section className=\"mb-8\">\r\n                    <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\r\n                      <h3 className=\"text-lg font-semibold text-foreground mb-4 flex items-center\">\r\n                        <svg className=\"w-5 h-5 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                        </svg>\r\n                        Recent Posts\r\n                      </h3>\r\n                      <div className=\"space-y-4\">\r\n                        {posts\r\n                          .filter(post => post.id !== featuredPost?.id)\r\n                          .slice(0, 3)\r\n                          .map((post) => (\r\n                          <div key={post.id} className=\"group\">\r\n                            <Link href={`/posts/${post.id}`} className=\"block\">\r\n                              <h4 className=\"text-sm font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-1\">\r\n                                {post.title}\r\n                              </h4>\r\n                              <p className=\"text-xs text-muted-foreground\">\r\n                                {formatDate(post.created_at)}\r\n                              </p>\r\n                            </Link>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  </section>\r\n                )}\r\n              </aside>\r\n            </div>\r\n          </>\r\n        )}\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAkBA,MAAM,iBAAiB;AAEhB,SAAS,eAAe,EAAE,cAAc,KAAK,EAAE,IAAI,EAAE,WAAW,EAAuB;;IAC5F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,YAAY;QACd;mCAAG,EAAE;IAEL,2BAA2B;IAC3B,QAAQ,GAAG,CAAC,wCAAwC,aAAa,gBAAgB,aAAa,aAAa;IAE3G,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,QAAQ,GAAG,CAAC,uCAAuC;QACrD;mCAAG;QAAC;KAAY;IAEhB,kDAAkD;IAClD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,OAAO,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5B;gDAAG;QAAC;QAAO;KAAY;IAEvB,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,QAAQ,GAAG,CAAC,wCAAwC,aAAa,yBAAyB,cAAc,MAAM;YAC9G,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,aAAa;YACzD,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,sEAAsE;YACtE,oEAAoE;YACpE,IAAI,YAAY,OAAO,UAAU,GAAG,KAAK,cAAc,OAAO,UAAU,EAAE;gBACxE,QAAQ,GAAG,CAAC,yDAAyD,aAAa,MAAM,OAAO,UAAU;gBACzG,qDAAqD;gBACrD;6DAAW,IAAM,eAAe,OAAO,UAAU;4DAAG;YACtD;YAEA,OAAO;QACT;gDAAG;QAAC;QAAe;KAAY;IAE/B,sDAAsD;IACtD,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB,CAAC;QACxB,yCAAyC;QACzC,IAAI,CAAC,UAAU;YACb,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,kDAAkD;YAC5D,eAAe;YACf;YACA,YAAY,cAAc,MAAM;YAChC,cAAc;QAChB;QAEA,MAAM,aAAa,KAAK,IAAI,CAAC,cAAc,MAAM,GAAG,mBAAmB;QACvE,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;QAE7C,QAAQ,GAAG,CAAC,4CAA4C;YACtD;YACA;YACA,YAAY,cAAc;QAC5B;QAEA,IAAI,cAAc,aAAa;YAC7B,eAAe;QACjB;IACF;IAGA,uCAAuC;IACvC,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;IAGnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAiG;;;;;;0CAG1H,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,kJAAA,CAAA,oBAAiB;;;;;oCACjB,qBACC;;0DACE,6LAAC;gDAAK,WAAU;;oDAAkE;oDACtE,KAAK,KAAK;;;;;;;4CAGrB,6BACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,6BACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;0DAGxC,6LAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,6LAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;4DAAkB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACzE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;qEAKzC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAK,WAAU;0BACb,MAAM,MAAM,KAAK,IAChB,eAAe,iBACf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAgC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACvF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;4BACzC,6BACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;yCAOd;8BAEE,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAQ,IAAG;wCAAQ,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA2D;;;;;;8EACzE,6LAAC;oEAAE,WAAU;;wEACV,MAAM,MAAM;wEAAC;wEAAM,MAAM,MAAM,KAAK,IAAI,KAAK;wEAAI;;;;;;;;;;;;;sEAGtD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,sIAAA,CAAA,YAAS;gEAAC,UAAU;gEAAc,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAOnD,6LAAC,sIAAA,CAAA,gBAAa;gDAAC,OAAO;gDAAa,cAAc,cAAc,MAAM;;;;;;;;;;;;kDAIvE,6LAAC;wCAAQ,WAAU;kDAChB,cAAc,KAAK,CAAC,MAAM,KAAK,kBAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAkC,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACzF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAClD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS;wDACP,eAAe;wDACf,iBAAiB;oDACnB;oDACA,WAAU;8DACX;;;;;;;;;;;iEAKH,6LAAC;4CAAI,WAAU;sDACZ,cAAc,KAAK,CACjB,GAAG,CAAC,CAAC,MAAM,sBACZ,6LAAC,qIAAA,CAAA,WAAQ;oDAEP,MAAM;oDACN,WAAU;oDACV,OAAO;wDACL,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;oDACpC;mDALK,KAAK,EAAE;;;;;;;;;;;;;;;oCAarB,cAAc,UAAU,GAAG,mBAC1B,6LAAC;wCAAQ,WAAU;;0DACjB,6LAAC,mIAAA,CAAA,aAAU;gDACT,aAAa,cAAc,WAAW;gDACtC,YAAY,cAAc,UAAU;gDACpC,cAAc;;;;;;0DAEhB,6LAAC,mIAAA,CAAA,iBAAc;gDACb,aAAa,cAAc,WAAW;gDACtC,YAAY,cAAc,UAAU;gDACpC,YAAY,cAAc,UAAU;gDACpC,cAAc;;;;;;;;;;;;;;;;;;0CAOtB,6LAAC;gCAAM,WAAU;;kDAEf,6LAAC,4IAAA,CAAA,kBAAe;;;;;oCAGf,MAAM,MAAM,GAAG,mBACd,6LAAC;wCAAQ,WAAU;kDACjB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA4B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACnF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAI,WAAU;8DACZ,MACE,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,IACzC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,qBACN,6LAAC;4DAAkB,WAAU;sEAC3B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gEAAE,WAAU;;kFACzC,6LAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,6LAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;2DANvB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsB3C;GApSgB;KAAA", "debugId": null}}]}