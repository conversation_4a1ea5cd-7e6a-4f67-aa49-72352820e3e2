(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/homepage-client.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_19047f48._.js",
  "static/chunks/node_modules_e87755dd._.js",
  "static/chunks/src_components_homepage-client_tsx_21b52e56._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/homepage-client.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);